"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star } from "lucide-react"
import type { ProductData, ProductRecommendation } from "@/lib/product-database"

interface ProductRecommendationsProps {
  currentProduct: ProductData
  userPreferences?: any
}

export function ProductRecommendations({ currentProduct, userPreferences }: ProductRecommendationsProps) {
  const [recommendations, setRecommendations] = useState<ProductRecommendation[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const fetchRecommendations = async () => {
      setLoading(true)
      try {
        const response = await fetch("/api/recommendations", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ product: currentProduct, userPreferences }),
        })

        if (response.ok) {
          const data = await response.json()
          setRecommendations(data.recommendations || [])
        }
      } catch (error) {
        console.error("Error fetching recommendations:", error)
      } finally {
        setLoading(false)
      }
    }

    if (currentProduct.categories?.length) {
      fetchRecommendations()
    }
  }, [currentProduct, userPreferences])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Better Alternatives
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-muted-foreground">Finding healthier alternatives...</div>
        </CardContent>
      </Card>
    )
  }

  if (recommendations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Better Alternatives
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-muted-foreground">No alternatives found for this product category.</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Star className="h-5 w-5" />
          Better Alternatives
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {recommendations.map((rec, index) => (
          <div key={rec.product.barcode || index} className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex-1">
              <h4 className="font-medium">{rec.product.productName}</h4>
              {rec.product.brand && <p className="text-sm text-muted-foreground">{rec.product.brand}</p>}
              <p className="text-sm text-green-600 mt-1">{rec.reason}</p>
              <div className="flex gap-2 mt-2">
                {rec.product.nutritionGrade && (
                  <Badge variant={rec.product.nutritionGrade <= "b" ? "default" : "secondary"}>
                    Grade {rec.product.nutritionGrade.toUpperCase()}
                  </Badge>
                )}
                <Badge variant="outline">Score: {rec.score}</Badge>
              </div>
            </div>
            {rec.product.imageUrl && (
              <img
                src={rec.product.imageUrl || "/placeholder.svg"}
                alt={rec.product.productName}
                className="w-16 h-16 object-cover rounded ml-4"
              />
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
