import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { GeistSans } from "geist/font/sans"
import { GeistMono } from "geist/font/mono"
import "./globals.css"

export const metadata: Metadata = {
  title: "HealthScore - Scan Product Labels for Instant Health Scores",
  description:
    "Instantly analyze product labels with your camera and get personalized health scores. Make informed choices for a healthier lifestyle.",
  generator: "HealthScore App",
  keywords: ["health", "nutrition", "label scanning", "OCR", "food analysis", "health score"],
  authors: [{ name: "HealthScore Team" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#16a34a",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <style>{`
html {
  font-family: ${GeistSans.style.fontFamily};
  --font-sans: ${GeistSans.variable};
  --font-mono: ${GeistMono.variable};
}
        `}</style>
      </head>
      <body>{children}</body>
    </html>
  )
}
