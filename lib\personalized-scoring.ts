import type { NutritionFacts } from "./ocr-processor"
import type { UserPreferences, DietaryRestriction, HealthGoal } from "./user-profile"
import { calculateHealthScore } from "./health-scoring"

export interface PersonalizedScoreResult {
  baseScore: number
  personalizedScore: number
  adjustments: ScoreAdjustment[]
  warnings: string[]
  recommendations: string[]
}

export interface ScoreAdjustment {
  reason: string
  adjustment: number
  category: "dietary" | "allergy" | "goal" | "preference"
}

export const calculatePersonalizedScore = (
  nutritionFacts: NutritionFacts,
  ingredients: string[],
  preferences: UserPreferences,
): PersonalizedScoreResult => {
  const baseScore = calculateHealthScore(nutritionFacts, ingredients)
  let personalizedScore = baseScore
  const adjustments: ScoreAdjustment[] = []
  const warnings: string[] = []
  const recommendations: string[] = []

  // Check for allergies
  preferences.allergies.forEach((allergy) => {
    const allergyFound = ingredients.some((ingredient) => ingredient.toLowerCase().includes(allergy.toLowerCase()))

    if (allergyFound) {
      personalizedScore -= 50 // Major penalty for allergens
      adjustments.push({
        reason: `Contains ${allergy} (allergen)`,
        adjustment: -50,
        category: "allergy",
      })
      warnings.push(`⚠️ Contains ${allergy} - This product may not be safe for you`)
    }
  })

  // Check dietary restrictions
  preferences.dietaryRestrictions.forEach((restriction) => {
    const violation = checkDietaryRestrictionViolation(restriction, ingredients, nutritionFacts)
    if (violation) {
      personalizedScore -= violation.penalty
      adjustments.push({
        reason: violation.reason,
        adjustment: -violation.penalty,
        category: "dietary",
      })
      warnings.push(`Does not meet ${getDietaryRestrictionLabel(restriction)} requirements: ${violation.reason}`)
    }
  })

  // Check avoided ingredients
  preferences.avoidIngredients.forEach((avoidedIngredient) => {
    const found = ingredients.some((ingredient) => ingredient.toLowerCase().includes(avoidedIngredient.toLowerCase()))

    if (found) {
      personalizedScore -= 15
      adjustments.push({
        reason: `Contains avoided ingredient: ${avoidedIngredient}`,
        adjustment: -15,
        category: "preference",
      })
    }
  })

  // Bonus for preferred ingredients
  preferences.preferredIngredients.forEach((preferredIngredient) => {
    const found = ingredients.some((ingredient) => ingredient.toLowerCase().includes(preferredIngredient.toLowerCase()))

    if (found) {
      personalizedScore += 5
      adjustments.push({
        reason: `Contains preferred ingredient: ${preferredIngredient}`,
        adjustment: 5,
        category: "preference",
      })
    }
  })

  // Apply health goal adjustments
  preferences.healthGoals.forEach((goal) => {
    const goalAdjustment = applyHealthGoalAdjustment(goal, nutritionFacts, ingredients)
    if (goalAdjustment) {
      personalizedScore += goalAdjustment.adjustment
      adjustments.push(goalAdjustment)

      if (goalAdjustment.adjustment < 0) {
        warnings.push(goalAdjustment.reason)
      }
    }
  })

  // Apply nutritional limits
  if (preferences.maxCalories && nutritionFacts.calories && nutritionFacts.calories > preferences.maxCalories) {
    const penalty = Math.min(20, Math.floor((nutritionFacts.calories - preferences.maxCalories) / 50) * 5)
    personalizedScore -= penalty
    adjustments.push({
      reason: `Exceeds your calorie limit (${preferences.maxCalories})`,
      adjustment: -penalty,
      category: "preference",
    })
  }

  if (preferences.maxSodium && nutritionFacts.sodium) {
    const sodium = Number.parseFloat(nutritionFacts.sodium)
    if (sodium > preferences.maxSodium) {
      const penalty = Math.min(15, Math.floor((sodium - preferences.maxSodium) / 100) * 3)
      personalizedScore -= penalty
      adjustments.push({
        reason: `Exceeds your sodium limit (${preferences.maxSodium}mg)`,
        adjustment: -penalty,
        category: "preference",
      })
    }
  }

  if (preferences.maxSugar && nutritionFacts.sugars) {
    const sugar = Number.parseFloat(nutritionFacts.sugars)
    if (sugar > preferences.maxSugar) {
      const penalty = Math.min(15, Math.floor((sugar - preferences.maxSugar) / 5) * 3)
      personalizedScore -= penalty
      adjustments.push({
        reason: `Exceeds your sugar limit (${preferences.maxSugar}g)`,
        adjustment: -penalty,
        category: "preference",
      })
    }
  }

  // Generate recommendations
  if (personalizedScore < baseScore - 10) {
    recommendations.push("Consider looking for alternatives that better match your dietary preferences")
  }

  if (warnings.length === 0 && personalizedScore >= 70) {
    recommendations.push("This product aligns well with your health goals and preferences")
  }

  // Ensure score stays within bounds
  personalizedScore = Math.max(0, Math.min(100, Math.round(personalizedScore)))

  return {
    baseScore,
    personalizedScore,
    adjustments,
    warnings,
    recommendations,
  }
}

const checkDietaryRestrictionViolation = (
  restriction: DietaryRestriction,
  ingredients: string[],
  nutritionFacts: NutritionFacts,
): { reason: string; penalty: number } | null => {
  const ingredientText = ingredients.join(" ").toLowerCase()

  switch (restriction) {
    case "vegetarian":
      if (
        ingredientText.includes("meat") ||
        ingredientText.includes("chicken") ||
        ingredientText.includes("beef") ||
        ingredientText.includes("pork") ||
        ingredientText.includes("fish") ||
        ingredientText.includes("gelatin")
      ) {
        return { reason: "Contains animal products", penalty: 30 }
      }
      break

    case "vegan":
      if (
        ingredientText.includes("milk") ||
        ingredientText.includes("cheese") ||
        ingredientText.includes("butter") ||
        ingredientText.includes("egg") ||
        ingredientText.includes("honey") ||
        ingredientText.includes("gelatin") ||
        ingredientText.includes("whey") ||
        ingredientText.includes("casein")
      ) {
        return { reason: "Contains animal-derived ingredients", penalty: 30 }
      }
      break

    case "gluten-free":
      if (
        ingredientText.includes("wheat") ||
        ingredientText.includes("barley") ||
        ingredientText.includes("rye") ||
        ingredientText.includes("gluten")
      ) {
        return { reason: "Contains gluten", penalty: 40 }
      }
      break

    case "dairy-free":
      if (
        ingredientText.includes("milk") ||
        ingredientText.includes("cheese") ||
        ingredientText.includes("butter") ||
        ingredientText.includes("cream") ||
        ingredientText.includes("whey") ||
        ingredientText.includes("casein") ||
        ingredientText.includes("lactose")
      ) {
        return { reason: "Contains dairy", penalty: 35 }
      }
      break

    case "keto":
      if (nutritionFacts.totalCarbs && Number.parseFloat(nutritionFacts.totalCarbs) > 5) {
        return { reason: "Too high in carbohydrates for keto diet", penalty: 25 }
      }
      break

    case "low-sodium":
      if (nutritionFacts.sodium && Number.parseFloat(nutritionFacts.sodium) > 140) {
        return { reason: "High sodium content", penalty: 20 }
      }
      break

    case "diabetic-friendly":
      if (nutritionFacts.sugars && Number.parseFloat(nutritionFacts.sugars) > 10) {
        return { reason: "High sugar content", penalty: 25 }
      }
      break
  }

  return null
}

const applyHealthGoalAdjustment = (
  goal: HealthGoal,
  nutritionFacts: NutritionFacts,
  ingredients: string[],
): ScoreAdjustment | null => {
  switch (goal) {
    case "weight-loss":
      if (nutritionFacts.calories && nutritionFacts.calories > 200) {
        return {
          reason: "High calorie content may not support weight loss goals",
          adjustment: -10,
          category: "goal",
        }
      }
      if (nutritionFacts.protein && Number.parseFloat(nutritionFacts.protein) > 5) {
        return {
          reason: "Good protein content supports weight loss",
          adjustment: 5,
          category: "goal",
        }
      }
      break

    case "heart-health":
      if (nutritionFacts.sodium && Number.parseFloat(nutritionFacts.sodium) > 300) {
        return {
          reason: "High sodium may not support heart health",
          adjustment: -15,
          category: "goal",
        }
      }
      if (nutritionFacts.saturatedFat && Number.parseFloat(nutritionFacts.saturatedFat) > 3) {
        return {
          reason: "High saturated fat may not support heart health",
          adjustment: -12,
          category: "goal",
        }
      }
      break

    case "muscle-gain":
      if (nutritionFacts.protein && Number.parseFloat(nutritionFacts.protein) > 10) {
        return {
          reason: "High protein content supports muscle gain",
          adjustment: 8,
          category: "goal",
        }
      }
      break

    case "diabetes-management":
      if (nutritionFacts.sugars && Number.parseFloat(nutritionFacts.sugars) > 8) {
        return {
          reason: "High sugar content may not support diabetes management",
          adjustment: -20,
          category: "goal",
        }
      }
      break
  }

  return null
}

const getDietaryRestrictionLabel = (restriction: DietaryRestriction): string => {
  const labels: Record<DietaryRestriction, string> = {
    vegetarian: "Vegetarian",
    vegan: "Vegan",
    "gluten-free": "Gluten-Free",
    "dairy-free": "Dairy-Free",
    keto: "Ketogenic",
    paleo: "Paleo",
    "low-carb": "Low Carb",
    "low-fat": "Low Fat",
    "low-sodium": "Low Sodium",
    "diabetic-friendly": "Diabetic Friendly",
  }
  return labels[restriction]
}
