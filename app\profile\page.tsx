"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, History, Plus, X, Save } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { HealthScoreBadge } from "@/components/health-score-badge"
import {
  loadUserProfile,
  createUserProfile,
  updateUserPreferences,
  getScanHistory,
  clearUserData,
  getDietaryRestrictionLabel,
  getHealthGoalLabel,
  type UserProfile,
  type DietaryRestriction,
  type HealthGoal,
} from "@/lib/user-profile"
import Link from "next/link"

const DIETARY_RESTRICTIONS: DietaryRestriction[] = [
  "vegetarian",
  "vegan",
  "gluten-free",
  "dairy-free",
  "keto",
  "paleo",
  "low-carb",
  "low-fat",
  "low-sodium",
  "diabetic-friendly",
]

const HEALTH_GOALS: HealthGoal[] = [
  "weight-loss",
  "muscle-gain",
  "heart-health",
  "diabetes-management",
  "general-wellness",
  "energy-boost",
  "digestive-health",
]

export default function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [newAllergy, setNewAllergy] = useState("")
  const [newAvoidIngredient, setNewAvoidIngredient] = useState("")
  const [newPreferredIngredient, setNewPreferredIngredient] = useState("")

  useEffect(() => {
    const existingProfile = loadUserProfile()
    if (existingProfile) {
      setProfile(existingProfile)
    } else {
      // Create a new profile for first-time users
      const newProfile = createUserProfile()
      setProfile(newProfile)
      setIsEditing(true) // Start in editing mode for new users
    }
  }, [])

  const handleSaveProfile = () => {
    if (profile) {
      setProfile({ ...profile })
      setIsEditing(false)
    }
  }

  const handleDietaryRestrictionChange = (restriction: DietaryRestriction, checked: boolean) => {
    if (!profile) return

    const updatedRestrictions = checked
      ? [...profile.preferences.dietaryRestrictions, restriction]
      : profile.preferences.dietaryRestrictions.filter((r) => r !== restriction)

    const updatedProfile = updateUserPreferences({ dietaryRestrictions: updatedRestrictions })
    if (updatedProfile) setProfile(updatedProfile)
  }

  const handleHealthGoalChange = (goal: HealthGoal, checked: boolean) => {
    if (!profile) return

    const updatedGoals = checked
      ? [...profile.preferences.healthGoals, goal]
      : profile.preferences.healthGoals.filter((g) => g !== goal)

    const updatedProfile = updateUserPreferences({ healthGoals: updatedGoals })
    if (updatedProfile) setProfile(updatedProfile)
  }

  const addAllergy = () => {
    if (!profile || !newAllergy.trim()) return

    const updatedAllergies = [...profile.preferences.allergies, newAllergy.trim()]
    const updatedProfile = updateUserPreferences({ allergies: updatedAllergies })
    if (updatedProfile) {
      setProfile(updatedProfile)
      setNewAllergy("")
    }
  }

  const removeAllergy = (allergy: string) => {
    if (!profile) return

    const updatedAllergies = profile.preferences.allergies.filter((a) => a !== allergy)
    const updatedProfile = updateUserPreferences({ allergies: updatedAllergies })
    if (updatedProfile) setProfile(updatedProfile)
  }

  const addAvoidIngredient = () => {
    if (!profile || !newAvoidIngredient.trim()) return

    const updatedIngredients = [...profile.preferences.avoidIngredients, newAvoidIngredient.trim()]
    const updatedProfile = updateUserPreferences({ avoidIngredients: updatedIngredients })
    if (updatedProfile) {
      setProfile(updatedProfile)
      setNewAvoidIngredient("")
    }
  }

  const removeAvoidIngredient = (ingredient: string) => {
    if (!profile) return

    const updatedIngredients = profile.preferences.avoidIngredients.filter((i) => i !== ingredient)
    const updatedProfile = updateUserPreferences({ avoidIngredients: updatedIngredients })
    if (updatedProfile) setProfile(updatedProfile)
  }

  const addPreferredIngredient = () => {
    if (!profile || !newPreferredIngredient.trim()) return

    const updatedIngredients = [...profile.preferences.preferredIngredients, newPreferredIngredient.trim()]
    const updatedProfile = updateUserPreferences({ preferredIngredients: updatedIngredients })
    if (updatedProfile) {
      setProfile(updatedProfile)
      setNewPreferredIngredient("")
    }
  }

  const removePreferredIngredient = (ingredient: string) => {
    if (!profile) return

    const updatedIngredients = profile.preferences.preferredIngredients.filter((i) => i !== ingredient)
    const updatedProfile = updateUserPreferences({ preferredIngredients: updatedIngredients })
    if (updatedProfile) setProfile(updatedProfile)
  }

  const handleClearData = () => {
    if (confirm("Are you sure you want to clear all your data? This cannot be undone.")) {
      clearUserData()
      const newProfile = createUserProfile()
      setProfile(newProfile)
      setIsEditing(true)
    }
  }

  const scanHistory = getScanHistory()

  if (!profile) {
    return <div className="min-h-screen bg-gray-50 flex items-center justify-center">Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/">
                <Button variant="ghost" size="sm" className="mr-4">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
              </Link>
              <h1 className="text-xl font-semibold">Your Profile</h1>
            </div>
            <Button
              onClick={isEditing ? handleSaveProfile : () => setIsEditing(true)}
              className="bg-green-600 hover:bg-green-700"
            >
              {isEditing ? (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              ) : (
                <>
                  <Settings className="h-4 w-4 mr-2" />
                  Edit Profile
                </>
              )}
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="preferences" className="max-w-4xl mx-auto">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
            <TabsTrigger value="history">Scan History</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="preferences" className="space-y-6">
            {/* Dietary Restrictions */}
            <Card>
              <CardHeader>
                <CardTitle>Dietary Restrictions</CardTitle>
                <CardDescription>
                  Select any dietary restrictions that apply to you. This will help personalize your health scores.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {DIETARY_RESTRICTIONS.map((restriction) => (
                    <div key={restriction} className="flex items-center space-x-2">
                      <Checkbox
                        id={restriction}
                        checked={profile.preferences.dietaryRestrictions.includes(restriction)}
                        onCheckedChange={(checked) => handleDietaryRestrictionChange(restriction, checked as boolean)}
                        disabled={!isEditing}
                      />
                      <Label htmlFor={restriction} className="text-sm">
                        {getDietaryRestrictionLabel(restriction)}
                      </Label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Health Goals */}
            <Card>
              <CardHeader>
                <CardTitle>Health Goals</CardTitle>
                <CardDescription>What are your primary health and wellness goals?</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {HEALTH_GOALS.map((goal) => (
                    <div key={goal} className="flex items-center space-x-2">
                      <Checkbox
                        id={goal}
                        checked={profile.preferences.healthGoals.includes(goal)}
                        onCheckedChange={(checked) => handleHealthGoalChange(goal, checked as boolean)}
                        disabled={!isEditing}
                      />
                      <Label htmlFor={goal} className="text-sm">
                        {getHealthGoalLabel(goal)}
                      </Label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Allergies */}
            <Card>
              <CardHeader>
                <CardTitle>Allergies</CardTitle>
                <CardDescription>
                  List any food allergies. Products containing these will receive very low scores.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {profile.preferences.allergies.map((allergy) => (
                      <Badge key={allergy} variant="destructive" className="flex items-center gap-1">
                        {allergy}
                        {isEditing && <X className="h-3 w-3 cursor-pointer" onClick={() => removeAllergy(allergy)} />}
                      </Badge>
                    ))}
                  </div>
                  {isEditing && (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add allergy (e.g., peanuts, shellfish)"
                        value={newAllergy}
                        onChange={(e) => setNewAllergy(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && addAllergy()}
                      />
                      <Button onClick={addAllergy} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Ingredients to Avoid */}
            <Card>
              <CardHeader>
                <CardTitle>Ingredients to Avoid</CardTitle>
                <CardDescription>Ingredients you prefer to avoid (but aren't allergic to).</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {profile.preferences.avoidIngredients.map((ingredient) => (
                      <Badge key={ingredient} variant="secondary" className="flex items-center gap-1">
                        {ingredient}
                        {isEditing && (
                          <X className="h-3 w-3 cursor-pointer" onClick={() => removeAvoidIngredient(ingredient)} />
                        )}
                      </Badge>
                    ))}
                  </div>
                  {isEditing && (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add ingredient to avoid"
                        value={newAvoidIngredient}
                        onChange={(e) => setNewAvoidIngredient(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && addAvoidIngredient()}
                      />
                      <Button onClick={addAvoidIngredient} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Preferred Ingredients */}
            <Card>
              <CardHeader>
                <CardTitle>Preferred Ingredients</CardTitle>
                <CardDescription>Ingredients you like to see in products.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {profile.preferences.preferredIngredients.map((ingredient) => (
                      <Badge
                        key={ingredient}
                        variant="default"
                        className="flex items-center gap-1 bg-green-100 text-green-800"
                      >
                        {ingredient}
                        {isEditing && (
                          <X className="h-3 w-3 cursor-pointer" onClick={() => removePreferredIngredient(ingredient)} />
                        )}
                      </Badge>
                    ))}
                  </div>
                  {isEditing && (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add preferred ingredient"
                        value={newPreferredIngredient}
                        onChange={(e) => setNewPreferredIngredient(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && addPreferredIngredient()}
                      />
                      <Button onClick={addPreferredIngredient} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Scan History</CardTitle>
                <CardDescription>Your recent product scans and health scores.</CardDescription>
              </CardHeader>
              <CardContent>
                {scanHistory.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No scans yet. Start scanning products to see your history here!</p>
                    <Link href="/scan">
                      <Button className="mt-4 bg-green-600 hover:bg-green-700">Start Scanning</Button>
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {scanHistory.slice(0, 10).map((scan) => (
                      <div key={scan.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{scan.productName}</h4>
                          <p className="text-sm text-gray-500">
                            {scan.scannedAt.toLocaleDateString()} at {scan.scannedAt.toLocaleTimeString()}
                          </p>
                        </div>
                        <HealthScoreBadge score={scan.healthScore} size="sm" />
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Account Settings</CardTitle>
                <CardDescription>Manage your account and data preferences.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="profile-name">Name (Optional)</Label>
                  <Input
                    id="profile-name"
                    value={profile.name || ""}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                    placeholder="Enter your name"
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="profile-email">Email (Optional)</Label>
                  <Input
                    id="profile-email"
                    type="email"
                    value={profile.email || ""}
                    onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                    placeholder="Enter your email"
                    disabled={!isEditing}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Data Management</CardTitle>
                <CardDescription>Manage your stored data and preferences.</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="destructive" onClick={handleClearData} className="w-full">
                  Clear All Data
                </Button>
                <p className="text-xs text-gray-500 mt-2">
                  This will permanently delete your profile, preferences, and scan history.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
