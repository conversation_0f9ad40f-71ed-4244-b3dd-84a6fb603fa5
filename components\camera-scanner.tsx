"use client"

import type React from "react"
import { useState, useRef, useCallback, useEffect } from "react"
import { Camera, Upload, X, <PERSON><PERSON><PERSON>riangle, RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface CameraScannerProps {
  onImageCapture: (imageData: string) => void
  onClose?: () => void
}

export function CameraScanner({ onImageCapture, onClose }: CameraScannerProps) {
  const [isActive, setIsActive] = useState(false)
  const [stream, setStream] = useState<MediaStream | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Check camera permission on mount
  useEffect(() => {
    checkCameraPermission()
  }, [])

  const checkCameraPermission = async () => {
    try {
      const permission = await navigator.permissions.query({ name: "camera" as PermissionName })
      setHasPermission(permission.state === "granted")
    } catch (error) {
      // Fallback for browsers that don't support permissions API
      setHasPermission(null)
    }
  }

  const startCamera = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "environment", // Use back camera on mobile
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      })

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
        setStream(mediaStream)
        setIsActive(true)
        setHasPermission(true)
      }
    } catch (error: any) {
      console.error("Error accessing camera:", error)

      let errorMessage = "Unable to access camera. "

      if (error.name === "NotAllowedError") {
        errorMessage += "Please allow camera access and try again."
        setHasPermission(false)
      } else if (error.name === "NotFoundError") {
        errorMessage += "No camera found on this device."
      } else if (error.name === "NotSupportedError") {
        errorMessage += "Camera not supported in this browser."
      } else {
        errorMessage += "Please try uploading an image instead."
      }

      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop())
      setStream(null)
    }
    setIsActive(false)
  }, [stream])

  const captureImage = useCallback(() => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current
      const video = videoRef.current

      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      const ctx = canvas.getContext("2d")
      if (ctx) {
        ctx.drawImage(video, 0, 0)
        const imageData = canvas.toDataURL("image/jpeg", 0.8)
        onImageCapture(imageData)
        stopCamera()
      }
    }
  }, [onImageCapture, stopCamera])

  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0]
      if (file) {
        // Validate file type
        if (!file.type.startsWith("image/")) {
          setError("Please select a valid image file.")
          return
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setError("Image file is too large. Please select a file under 10MB.")
          return
        }

        const reader = new FileReader()
        reader.onload = (e) => {
          const imageData = e.target?.result as string
          onImageCapture(imageData)
        }
        reader.onerror = () => {
          setError("Failed to read the image file. Please try again.")
        }
        reader.readAsDataURL(file)
      }
    },
    [onImageCapture],
  )

  const retryCamera = () => {
    setError(null)
    startCamera()
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Scan Product Label</h3>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {error && (
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!isActive ? (
          <div className="space-y-4">
            <Button
              onClick={startCamera}
              disabled={isLoading}
              className="w-full bg-green-600 hover:bg-green-700"
              size="lg"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-5 w-5 animate-spin" />
                  Starting Camera...
                </>
              ) : (
                <>
                  <Camera className="mr-2 h-5 w-5" />
                  Use Camera
                </>
              )}
            </Button>

            {error && hasPermission === false && (
              <Button variant="outline" onClick={retryCamera} className="w-full bg-transparent">
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry Camera Access
              </Button>
            )}

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">Or</span>
              </div>
            </div>

            <Button variant="outline" onClick={() => fileInputRef.current?.click()} className="w-full" size="lg">
              <Upload className="mr-2 h-5 w-5" />
              Upload Image
            </Button>
            <input ref={fileInputRef} type="file" accept="image/*" onChange={handleFileUpload} className="hidden" />

            <p className="text-xs text-gray-500 text-center">
              For best results, ensure the label is well-lit and clearly visible
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="relative bg-black rounded-lg overflow-hidden">
              <video ref={videoRef} autoPlay playsInline className="w-full h-64 object-cover" />
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="w-48 h-32 border-2 border-green-400 rounded-lg">
                  <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-green-400"></div>
                  <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-green-400"></div>
                  <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-green-400"></div>
                  <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-green-400"></div>
                </div>
              </div>
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black bg-opacity-50 px-2 py-1 rounded">
                Align label within frame
              </div>
            </div>

            <div className="flex space-x-2">
              <Button onClick={captureImage} className="flex-1 bg-green-600 hover:bg-green-700">
                <Camera className="mr-2 h-4 w-4" />
                Capture
              </Button>
              <Button variant="outline" onClick={stopCamera}>
                Cancel
              </Button>
            </div>
          </div>
        )}

        <canvas ref={canvasRef} className="hidden" />
      </CardContent>
    </Card>
  )
}
