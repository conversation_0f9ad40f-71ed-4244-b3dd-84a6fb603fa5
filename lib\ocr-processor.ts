export interface NutritionFacts {
  calories?: number
  totalFat?: string
  saturatedFat?: string
  transFat?: string
  cholesterol?: string
  sodium?: string
  totalCarbs?: string
  dietaryFiber?: string
  sugars?: string
  addedSugars?: string
  protein?: string
  servingSize?: string
  servingsPerContainer?: string
}

export interface OCRResult {
  text: string
  confidence: number
  nutritionFacts: NutritionFacts
  ingredients: string[]
  productName?: string
  barcode?: string
}

// Enhanced sample nutrition data with more variety
const sampleNutritionData = [
  {
    productName: "Organic Whole Grain Cereal",
    barcode: "1234567890123",
    category: "cereal",
    nutritionFacts: {
      calories: 110,
      totalFat: "1.5",
      saturatedFat: "0",
      sodium: "190",
      totalCarbs: "23",
      dietaryFiber: "3",
      sugars: "4",
      protein: "3",
      servingSize: "3/4 cup (30g)",
    },
    ingredients: ["whole grain oats", "sugar", "salt", "natural flavor", "vitamin E"],
  },
  {
    productName: "Greek Yogurt",
    barcode: "9876543210987",
    category: "dairy",
    nutritionFacts: {
      calories: 100,
      totalFat: "0",
      saturatedFat: "0",
      sodium: "65",
      totalCarbs: "6",
      sugars: "4",
      protein: "17",
      servingSize: "1 container (170g)",
    },
    ingredients: ["cultured grade A non fat milk", "sugar", "natural flavors", "fruit pectin"],
  },
  {
    productName: "Chocolate Chip Cookies",
    barcode: "5555666677778",
    category: "snack",
    nutritionFacts: {
      calories: 160,
      totalFat: "8",
      saturatedFat: "3.5",
      sodium: "105",
      totalCarbs: "21",
      sugars: "11",
      protein: "2",
      servingSize: "2 cookies (28g)",
    },
    ingredients: [
      "enriched flour",
      "sugar",
      "chocolate chips",
      "palm oil",
      "eggs",
      "baking soda",
      "salt",
      "natural vanilla flavor",
    ],
  },
  {
    productName: "Orange Juice",
    barcode: "1111222233334",
    category: "beverage",
    nutritionFacts: {
      calories: 110,
      totalFat: "0",
      saturatedFat: "0",
      sodium: "0",
      totalCarbs: "26",
      sugars: "22",
      protein: "2",
      servingSize: "8 fl oz (240ml)",
    },
    ingredients: ["orange juice", "natural flavors", "vitamin C"],
  },
  {
    productName: "Whole Wheat Bread",
    barcode: "4444555566667",
    category: "bread",
    nutritionFacts: {
      calories: 80,
      totalFat: "1",
      saturatedFat: "0",
      sodium: "150",
      totalCarbs: "15",
      dietaryFiber: "3",
      sugars: "2",
      protein: "4",
      servingSize: "1 slice (28g)",
    },
    ingredients: ["whole wheat flour", "water", "yeast", "salt", "honey", "wheat gluten"],
  },
  {
    productName: "Potato Chips",
    barcode: "7777888899990",
    category: "snack",
    nutritionFacts: {
      calories: 150,
      totalFat: "10",
      saturatedFat: "1.5",
      sodium: "180",
      totalCarbs: "15",
      sugars: "1",
      protein: "2",
      servingSize: "1 oz (28g)",
    },
    ingredients: ["potatoes", "vegetable oil", "salt"],
  },
  {
    productName: "Tomato Pasta Sauce",
    barcode: "2222333344445",
    category: "sauce",
    nutritionFacts: {
      calories: 70,
      totalFat: "0.5",
      saturatedFat: "0",
      sodium: "460",
      totalCarbs: "16",
      sugars: "12",
      protein: "3",
      servingSize: "1/2 cup (125g)",
    },
    ingredients: ["tomatoes", "tomato puree", "sugar", "salt", "garlic", "onion", "basil", "oregano"],
  },
  {
    productName: "Granola Bar",
    barcode: "8888999900001",
    category: "snack",
    nutritionFacts: {
      calories: 140,
      totalFat: "5",
      saturatedFat: "1",
      sodium: "95",
      totalCarbs: "22",
      dietaryFiber: "3",
      sugars: "7",
      protein: "3",
      servingSize: "1 bar (35g)",
    },
    ingredients: ["oats", "honey", "almonds", "dried cranberries", "sunflower oil", "salt"],
  },
]

// Generate realistic variations based on image hash
const generateVariation = (imageData: string, baseProduct: any) => {
  // Create a simple hash from image data to ensure consistency
  let hash = 0
  for (let i = 0; i < Math.min(imageData.length, 100); i++) {
    hash = ((hash << 5) - hash + imageData.charCodeAt(i)) & 0xffffffff
  }

  // Use hash to create consistent but varied nutrition facts
  const variation = Math.abs(hash) % 100

  const varied = { ...baseProduct }

  // Apply realistic variations (±10-20% for most values)
  if (varied.nutritionFacts.calories) {
    const calorieVariation = 1 + ((variation % 20) - 10) / 100 // ±10%
    varied.nutritionFacts.calories = Math.round(varied.nutritionFacts.calories * calorieVariation)
  }

  if (varied.nutritionFacts.totalFat) {
    const fatVariation = 1 + ((variation % 30) - 15) / 100 // ±15%
    varied.nutritionFacts.totalFat = (Number.parseFloat(varied.nutritionFacts.totalFat) * fatVariation).toFixed(1)
  }

  if (varied.nutritionFacts.sodium) {
    const sodiumVariation = 1 + ((variation % 40) - 20) / 100 // ±20%
    varied.nutritionFacts.sodium = Math.round(
      Number.parseFloat(varied.nutritionFacts.sodium) * sodiumVariation,
    ).toString()
  }

  if (varied.nutritionFacts.sugars) {
    const sugarVariation = 1 + ((variation % 25) - 12) / 100 // ±12%
    varied.nutritionFacts.sugars = Math.round(
      Number.parseFloat(varied.nutritionFacts.sugars) * sugarVariation,
    ).toString()
  }

  if (varied.nutritionFacts.protein) {
    const proteinVariation = 1 + ((variation % 20) - 10) / 100 // ±10%
    varied.nutritionFacts.protein = (Number.parseFloat(varied.nutritionFacts.protein) * proteinVariation).toFixed(1)
  }

  return varied
}

// Simulate barcode extraction
export const extractBarcode = async (imageData: string): Promise<string | null> => {
  // Simulate processing delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  // Use image data to consistently select the same product for the same image
  let hash = 0
  for (let i = 0; i < Math.min(imageData.length, 50); i++) {
    hash = ((hash << 5) - hash + imageData.charCodeAt(i)) & 0xffffffff
  }

  const productIndex = Math.abs(hash) % sampleNutritionData.length
  const selectedProduct = sampleNutritionData[productIndex]

  return selectedProduct.barcode
}

// Simulate OCR text extraction
export const extractTextFromImage = async (imageData: string): Promise<{ text: string; confidence: number }> => {
  // Simulate processing delay
  await new Promise((resolve) => setTimeout(resolve, 1500))

  // Use image data to consistently select the same product for the same image
  let hash = 0
  for (let i = 0; i < Math.min(imageData.length, 50); i++) {
    hash = ((hash << 5) - hash + imageData.charCodeAt(i)) & 0xffffffff
  }

  const productIndex = Math.abs(hash) % sampleNutritionData.length
  const baseProduct = sampleNutritionData[productIndex]

  // Generate realistic variation based on image
  const variedProduct = generateVariation(imageData, baseProduct)

  // Generate mock OCR text
  const mockText = `
${variedProduct.productName}

NUTRITION FACTS
Serving Size ${variedProduct.nutritionFacts.servingSize}

Amount Per Serving
Calories ${variedProduct.nutritionFacts.calories}

% Daily Value*
Total Fat ${variedProduct.nutritionFacts.totalFat}g
Saturated Fat ${variedProduct.nutritionFacts.saturatedFat}g
Sodium ${variedProduct.nutritionFacts.sodium}mg
Total Carbohydrate ${variedProduct.nutritionFacts.totalCarbs}g
${variedProduct.nutritionFacts.dietaryFiber ? `Dietary Fiber ${variedProduct.nutritionFacts.dietaryFiber}g` : ""}
Total Sugars ${variedProduct.nutritionFacts.sugars}g
Protein ${variedProduct.nutritionFacts.protein}g

INGREDIENTS: ${variedProduct.ingredients.join(", ")}
  `.trim()

  return {
    text: mockText,
    confidence: 85 + Math.random() * 10, // 85-95% confidence
  }
}

// Parse nutrition facts from OCR text
export const parseNutritionFacts = (text: string): NutritionFacts => {
  const nutritionFacts: NutritionFacts = {}

  // Common patterns for nutrition facts
  const patterns = {
    calories: /calories[:\s]*(\d+)/i,
    totalFat: /total fat[:\s]*(\d+(?:\.\d+)?)\s*g/i,
    saturatedFat: /saturated fat[:\s]*(\d+(?:\.\d+)?)\s*g/i,
    transFat: /trans fat[:\s]*(\d+(?:\.\d+)?)\s*g/i,
    cholesterol: /cholesterol[:\s]*(\d+)\s*mg/i,
    sodium: /sodium[:\s]*(\d+)\s*mg/i,
    totalCarbs: /total carbohydrate[s]?[:\s]*(\d+(?:\.\d+)?)\s*g/i,
    dietaryFiber: /dietary fiber[:\s]*(\d+(?:\.\d+)?)\s*g/i,
    sugars: /(?:total )?sugars[:\s]*(\d+(?:\.\d+)?)\s*g/i,
    addedSugars: /added sugars[:\s]*(\d+(?:\.\d+)?)\s*g/i,
    protein: /protein[:\s]*(\d+(?:\.\d+)?)\s*g/i,
    servingSize: /serving size[:\s]*([^\n\r]+)/i,
    servingsPerContainer: /servings per container[:\s]*(\d+(?:\.\d+)?)/i,
  }

  // Extract calories as number
  const caloriesMatch = text.match(patterns.calories)
  if (caloriesMatch) {
    nutritionFacts.calories = Number.parseInt(caloriesMatch[1])
  }

  // Extract other nutrition facts as strings with units
  const fatMatch = text.match(patterns.totalFat)
  if (fatMatch) {
    nutritionFacts.totalFat = `${fatMatch[1]}`
  }

  const satFatMatch = text.match(patterns.saturatedFat)
  if (satFatMatch) {
    nutritionFacts.saturatedFat = `${satFatMatch[1]}`
  }

  const transFatMatch = text.match(patterns.transFat)
  if (transFatMatch) {
    nutritionFacts.transFat = `${transFatMatch[1]}`
  }

  const cholesterolMatch = text.match(patterns.cholesterol)
  if (cholesterolMatch) {
    nutritionFacts.cholesterol = `${cholesterolMatch[1]}`
  }

  const sodiumMatch = text.match(patterns.sodium)
  if (sodiumMatch) {
    nutritionFacts.sodium = `${sodiumMatch[1]}`
  }

  const carbsMatch = text.match(patterns.totalCarbs)
  if (carbsMatch) {
    nutritionFacts.totalCarbs = `${carbsMatch[1]}`
  }

  const fiberMatch = text.match(patterns.dietaryFiber)
  if (fiberMatch) {
    nutritionFacts.dietaryFiber = `${fiberMatch[1]}`
  }

  const sugarsMatch = text.match(patterns.sugars)
  if (sugarsMatch) {
    nutritionFacts.sugars = `${sugarsMatch[1]}`
  }

  const addedSugarsMatch = text.match(patterns.addedSugars)
  if (addedSugarsMatch) {
    nutritionFacts.addedSugars = `${addedSugarsMatch[1]}`
  }

  const proteinMatch = text.match(patterns.protein)
  if (proteinMatch) {
    nutritionFacts.protein = `${proteinMatch[1]}`
  }

  const servingSizeMatch = text.match(patterns.servingSize)
  if (servingSizeMatch) {
    nutritionFacts.servingSize = servingSizeMatch[1].trim()
  }

  const servingsPerContainerMatch = text.match(patterns.servingsPerContainer)
  if (servingsPerContainerMatch) {
    nutritionFacts.servingsPerContainer = servingsPerContainerMatch[1]
  }

  return nutritionFacts
}

// Parse ingredients from OCR text
export const parseIngredients = (text: string): string[] => {
  // Look for ingredients section
  const ingredientsMatch = text.match(/ingredients[:\s]*([^.]+(?:\.[^.]*)*)/i)

  if (!ingredientsMatch) {
    return []
  }

  const ingredientsText = ingredientsMatch[1]

  // Split by commas and clean up
  const ingredients = ingredientsText
    .split(",")
    .map((ingredient) => ingredient.trim())
    .filter((ingredient) => ingredient.length > 0)
    .map((ingredient) => {
      // Remove parenthetical information and clean up
      return ingredient
        .replace(/$$[^)]*$$/g, "")
        .replace(/\s+/g, " ")
        .trim()
    })
    .filter((ingredient) => ingredient.length > 1)
    .slice(0, 20) // Limit to first 20 ingredients

  return ingredients
}

// Extract product name from OCR text
export const extractProductName = (text: string): string | undefined => {
  const lines = text.split("\n").filter((line) => line.trim().length > 0)

  // Usually the product name is in the first few lines and is in larger text
  // Look for lines that are likely product names (not nutrition facts)
  for (const line of lines.slice(0, 5)) {
    const cleanLine = line.trim()

    // Skip lines that look like nutrition facts or ingredients
    if (
      cleanLine.length > 3 &&
      cleanLine.length < 50 &&
      !cleanLine.toLowerCase().includes("nutrition") &&
      !cleanLine.toLowerCase().includes("ingredients") &&
      !cleanLine.toLowerCase().includes("calories") &&
      !/^\d+/.test(cleanLine) // Don't start with numbers
    ) {
      return cleanLine
    }
  }

  return undefined
}

// Main OCR processing function
export const processImageWithOCR = async (imageData: string): Promise<OCRResult> => {
  try {
    // Extract barcode first (faster)
    const barcode = await extractBarcode(imageData)

    // Extract text using OCR
    const { text, confidence } = await extractTextFromImage(imageData)

    // Parse the extracted text
    const nutritionFacts = parseNutritionFacts(text)
    const ingredients = parseIngredients(text)
    const productName = extractProductName(text)

    return {
      text,
      confidence,
      nutritionFacts,
      ingredients,
      productName,
      barcode,
    }
  } catch (error) {
    console.error("OCR Processing Error:", error)
    throw error
  }
}

// Cleanup function (no-op in demo version)
export const cleanupOCR = async () => {
  // No cleanup needed for demo version
}
