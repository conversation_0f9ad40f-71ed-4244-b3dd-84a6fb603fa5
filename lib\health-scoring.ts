import type { NutritionFacts } from "./ocr-processor"

export interface HealthScoreBreakdown {
  totalScore: number
  nutritionScore: number
  ingredientScore: number
  breakdown: {
    category: string
    score: number
    maxScore: number
    reason: string
  }[]
}

export const calculateHealthScore = (nutritionFacts: NutritionFacts, ingredients: string[]): number => {
  let score = 100 // Start with perfect score

  // Nutrition-based scoring
  if (nutritionFacts.calories) {
    if (nutritionFacts.calories > 400) score -= 15
    else if (nutritionFacts.calories > 250) score -= 8
    else if (nutritionFacts.calories < 50) score -= 5 // Too low might indicate artificial
  }

  if (nutritionFacts.totalFat) {
    const fat = Number.parseFloat(nutritionFacts.totalFat)
    if (fat > 20) score -= 15
    else if (fat > 10) score -= 8
  }

  if (nutritionFacts.saturatedFat) {
    const satFat = Number.parseFloat(nutritionFacts.saturatedFat)
    if (satFat > 10) score -= 20
    else if (satFat > 5) score -= 10
  }

  if (nutritionFacts.sugars) {
    const sugar = Number.parseFloat(nutritionFacts.sugars)
    if (sugar > 25) score -= 20
    else if (sugar > 15) score -= 12
    else if (sugar > 8) score -= 6
  }

  if (nutritionFacts.sodium) {
    const sodium = Number.parseFloat(nutritionFacts.sodium)
    if (sodium > 800) score -= 20
    else if (sodium > 400) score -= 12
    else if (sodium > 200) score -= 6
  }

  if (nutritionFacts.protein) {
    const protein = Number.parseFloat(nutritionFacts.protein)
    if (protein > 10) score += 5
    else if (protein > 5) score += 2
  }

  if (nutritionFacts.dietaryFiber) {
    const fiber = Number.parseFloat(nutritionFacts.dietaryFiber)
    if (fiber > 5) score += 8
    else if (fiber > 3) score += 4
  }

  // Ingredient-based scoring
  const negativeIngredients = [
    "high fructose corn syrup",
    "artificial colors",
    "artificial flavors",
    "preservatives",
    "monosodium glutamate",
    "trans fat",
    "hydrogenated oil",
    "corn syrup",
    "artificial sweeteners",
  ]

  const positiveIngredients = [
    "whole grain",
    "organic",
    "natural",
    "vitamin",
    "mineral",
    "antioxidant",
    "fiber",
    "protein",
  ]

  ingredients.forEach((ingredient) => {
    const lowerIngredient = ingredient.toLowerCase()

    negativeIngredients.forEach((negative) => {
      if (lowerIngredient.includes(negative)) {
        score -= 8
      }
    })

    positiveIngredients.forEach((positive) => {
      if (lowerIngredient.includes(positive)) {
        score += 3
      }
    })
  })

  // Ensure score is within bounds
  return Math.max(0, Math.min(100, Math.round(score)))
}

export const getDetailedHealthScore = (nutritionFacts: NutritionFacts, ingredients: string[]): HealthScoreBreakdown => {
  const breakdown: HealthScoreBreakdown["breakdown"] = []
  let nutritionScore = 50
  const ingredientScore = 50

  // Detailed nutrition scoring
  if (nutritionFacts.calories) {
    const calories = nutritionFacts.calories
    if (calories <= 100) {
      nutritionScore += 5
      breakdown.push({ category: "Calories", score: 5, maxScore: 5, reason: "Low calorie content" })
    } else if (calories > 300) {
      nutritionScore -= 10
      breakdown.push({ category: "Calories", score: -10, maxScore: 5, reason: "High calorie content" })
    }
  }

  // Add more detailed breakdowns...

  const totalScore = Math.max(0, Math.min(100, nutritionScore + ingredientScore))

  return {
    totalScore,
    nutritionScore: Math.max(0, Math.min(50, nutritionScore)),
    ingredientScore: Math.max(0, Math.min(50, ingredientScore)),
    breakdown,
  }
}
