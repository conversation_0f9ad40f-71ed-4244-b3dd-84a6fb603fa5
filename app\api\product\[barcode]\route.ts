import { type NextRequest, NextResponse } from "next/server"
import { searchProductByBarcode } from "@/lib/product-database"

export async function GET(request: NextRequest, { params }: { params: { barcode: string } }) {
  try {
    const { barcode } = params

    if (!barcode) {
      return NextResponse.json({ error: "Barcode is required" }, { status: 400 })
    }

    const productData = await searchProductByBarcode(barcode)

    if (!productData) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }

    return NextResponse.json(productData)
  } catch (error) {
    console.error("Error in product API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
