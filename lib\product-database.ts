export interface ProductData {
  barcode?: string
  productName: string
  brand?: string
  categories?: string[]
  nutritionGrade?: string
  novaGroup?: number
  ingredients?: string[]
  nutritionFacts?: {
    energy?: number
    fat?: number
    saturatedFat?: number
    carbohydrates?: number
    sugars?: number
    fiber?: number
    proteins?: number
    salt?: number
    sodium?: number
  }
  labels?: string[]
  allergens?: string[]
  imageUrl?: string
}

export interface ProductRecommendation {
  product: ProductData
  score: number
  reason: string
}

// Open Food Facts API integration
export const searchProductByBarcode = async (barcode: string): Promise<ProductData | null> => {
  try {
    const response = await fetch(`https://world.openfoodfacts.org/api/v0/product/${barcode}.json`)
    const data = await response.json()

    if (data.status === 0) return null

    const product = data.product
    return {
      barcode,
      productName: product.product_name || "Unknown Product",
      brand: product.brands,
      categories: product.categories?.split(",").map((c: string) => c.trim()),
      nutritionGrade: product.nutrition_grades,
      novaGroup: product.nova_group,
      ingredients: product.ingredients_text?.split(",").map((i: string) => i.trim()),
      nutritionFacts: {
        energy: product.nutriments?.energy_100g,
        fat: product.nutriments?.fat_100g,
        saturatedFat: product.nutriments?.["saturated-fat_100g"],
        carbohydrates: product.nutriments?.carbohydrates_100g,
        sugars: product.nutriments?.sugars_100g,
        fiber: product.nutriments?.fiber_100g,
        proteins: product.nutriments?.proteins_100g,
        salt: product.nutriments?.salt_100g,
        sodium: product.nutriments?.sodium_100g,
      },
      labels: product.labels?.split(",").map((l: string) => l.trim()),
      allergens: product.allergens?.split(",").map((a: string) => a.trim()),
      imageUrl: product.image_url,
    }
  } catch (error) {
    console.error("Error fetching product data:", error)
    return null
  }
}

// Search for similar products
export const findSimilarProducts = async (category: string, limit = 5): Promise<ProductData[]> => {
  try {
    const response = await fetch(
      `https://world.openfoodfacts.org/category/${encodeURIComponent(category)}.json?page_size=${limit}&fields=code,product_name,brands,nutrition_grades,nova_group,image_url`,
    )

    if (!response.ok) {
      console.warn(`API request failed with status: ${response.status}`)
      return getFallbackProducts(category, limit)
    }

    const contentType = response.headers.get("content-type")
    if (!contentType || !contentType.includes("application/json")) {
      console.warn("API returned non-JSON response")
      return getFallbackProducts(category, limit)
    }

    const data = await response.json()

    return (
      data.products?.map((product: any) => ({
        barcode: product.code,
        productName: product.product_name || "Unknown Product",
        brand: product.brands,
        nutritionGrade: product.nutrition_grades,
        novaGroup: product.nova_group,
        imageUrl: product.image_url,
      })) || getFallbackProducts(category, limit)
    )
  } catch (error) {
    console.error("Error finding similar products:", error)
    return getFallbackProducts(category, limit)
  }
}

const getFallbackProducts = (category: string, limit: number): ProductData[] => {
  const fallbackProducts: ProductData[] = [
    {
      barcode: "demo001",
      productName: "Organic Whole Grain Cereal",
      brand: "Nature's Best",
      nutritionGrade: "a",
      novaGroup: 1,
      imageUrl: "/organic-cereal-box.png",
    },
    {
      barcode: "demo002",
      productName: "Low Sugar Granola",
      brand: "Healthy Choice",
      nutritionGrade: "b",
      novaGroup: 2,
      imageUrl: "/granola-package.png",
    },
    {
      barcode: "demo003",
      productName: "Protein-Rich Oatmeal",
      brand: "Fit Foods",
      nutritionGrade: "a",
      novaGroup: 1,
      imageUrl: "/oatmeal-container.png",
    },
  ]

  return fallbackProducts.slice(0, limit)
}

// Get product recommendations based on health score
export const getProductRecommendations = async (
  currentProduct: ProductData,
  userPreferences: any,
): Promise<ProductRecommendation[]> => {
  if (!currentProduct.categories?.length) {
    return getFallbackRecommendations()
  }

  try {
    const category = currentProduct.categories[0]
    const similarProducts = await findSimilarProducts(category, 10)

    const recommendations: ProductRecommendation[] = similarProducts
      .filter((product) => product.barcode !== currentProduct.barcode)
      .map((product) => {
        let score = 50 // Base score
        let reason = "Similar product"

        // Boost score for better nutrition grade
        if (product.nutritionGrade) {
          const gradeScore = { a: 20, b: 10, c: 0, d: -10, e: -20 }[product.nutritionGrade.toLowerCase()]
          if (gradeScore !== undefined) {
            score += gradeScore
            if (gradeScore > 0) reason = `Better nutrition grade (${product.nutritionGrade.toUpperCase()})`
          }
        }

        // Boost score for lower NOVA group (less processed)
        if (product.novaGroup && currentProduct.novaGroup) {
          if (product.novaGroup < currentProduct.novaGroup) {
            score += 15
            reason = "Less processed alternative"
          }
        }

        return { product, score: Math.max(0, Math.min(100, score)), reason }
      })
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)

    return recommendations.length > 0 ? recommendations : getFallbackRecommendations()
  } catch (error) {
    console.error("Error getting recommendations:", error)
    return getFallbackRecommendations()
  }
}

const getFallbackRecommendations = (): ProductRecommendation[] => {
  return [
    {
      product: {
        barcode: "rec001",
        productName: "Organic Alternative",
        brand: "Pure Foods",
        nutritionGrade: "a",
        novaGroup: 1,
        imageUrl: "/organic-food-product.png",
      },
      score: 85,
      reason: "Better nutrition grade (A)",
    },
    {
      product: {
        barcode: "rec002",
        productName: "Low Sugar Option",
        brand: "Healthy Living",
        nutritionGrade: "b",
        novaGroup: 2,
        imageUrl: "/healthy-food-alternative.png",
      },
      score: 75,
      reason: "Less processed alternative",
    },
  ]
}
