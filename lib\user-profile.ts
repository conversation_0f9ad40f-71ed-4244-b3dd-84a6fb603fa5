export interface UserProfile {
  id: string
  name?: string
  email?: string
  createdAt: Date
  updatedAt: Date
  preferences: UserPreferences
  scanHistory: ScanHistoryItem[]
}

export interface UserPreferences {
  dietaryRestrictions: DietaryRestriction[]
  allergies: string[]
  healthGoals: HealthGoal[]
  avoidIngredients: string[]
  preferredIngredients: string[]
  maxCalories?: number
  maxSodium?: number
  maxSugar?: number
  minProtein?: number
  minFiber?: number
}

export interface ScanHistoryItem {
  id: string
  productName: string
  healthScore: number
  scannedAt: Date
  imageData?: string
  nutritionFacts: any
  ingredients: string[]
}

export type DietaryRestriction =
  | "vegetarian"
  | "vegan"
  | "gluten-free"
  | "dairy-free"
  | "keto"
  | "paleo"
  | "low-carb"
  | "low-fat"
  | "low-sodium"
  | "diabetic-friendly"

export type HealthGoal =
  | "weight-loss"
  | "muscle-gain"
  | "heart-health"
  | "diabetes-management"
  | "general-wellness"
  | "energy-boost"
  | "digestive-health"

const STORAGE_KEY = "healthscore_user_profile"

// Default user preferences
const defaultPreferences: UserPreferences = {
  dietaryRestrictions: [],
  allergies: [],
  healthGoals: [],
  avoidIngredients: [],
  preferredIngredients: [],
}

// Create a new user profile
export const createUserProfile = (name?: string, email?: string): UserProfile => {
  const profile: UserProfile = {
    id: generateId(),
    name,
    email,
    createdAt: new Date(),
    updatedAt: new Date(),
    preferences: defaultPreferences,
    scanHistory: [],
  }

  saveUserProfile(profile)
  return profile
}

// Load user profile from localStorage
export const loadUserProfile = (): UserProfile | null => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (!stored) return null

    const profile = JSON.parse(stored)
    // Convert date strings back to Date objects
    profile.createdAt = new Date(profile.createdAt)
    profile.updatedAt = new Date(profile.updatedAt)
    profile.scanHistory = profile.scanHistory.map((item: any) => ({
      ...item,
      scannedAt: new Date(item.scannedAt),
    }))

    return profile
  } catch (error) {
    console.error("Error loading user profile:", error)
    return null
  }
}

// Save user profile to localStorage
export const saveUserProfile = (profile: UserProfile): void => {
  try {
    profile.updatedAt = new Date()
    localStorage.setItem(STORAGE_KEY, JSON.stringify(profile))
  } catch (error) {
    console.error("Error saving user profile:", error)
  }
}

// Update user preferences
export const updateUserPreferences = (preferences: Partial<UserPreferences>): UserProfile | null => {
  const profile = loadUserProfile()
  if (!profile) return null

  profile.preferences = { ...profile.preferences, ...preferences }
  saveUserProfile(profile)
  return profile
}

// Add scan to history
export const addScanToHistory = (scanData: Omit<ScanHistoryItem, "id" | "scannedAt">): void => {
  const profile = loadUserProfile()
  if (!profile) return

  const historyItem: ScanHistoryItem = {
    ...scanData,
    id: generateId(),
    scannedAt: new Date(),
  }

  profile.scanHistory.unshift(historyItem) // Add to beginning

  // Keep only last 50 scans
  if (profile.scanHistory.length > 50) {
    profile.scanHistory = profile.scanHistory.slice(0, 50)
  }

  saveUserProfile(profile)
}

// Get user's scan history
export const getScanHistory = (): ScanHistoryItem[] => {
  const profile = loadUserProfile()
  return profile?.scanHistory || []
}

// Clear user data
export const clearUserData = (): void => {
  localStorage.removeItem(STORAGE_KEY)
}

// Generate simple ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// Get dietary restriction display names
export const getDietaryRestrictionLabel = (restriction: DietaryRestriction): string => {
  const labels: Record<DietaryRestriction, string> = {
    vegetarian: "Vegetarian",
    vegan: "Vegan",
    "gluten-free": "Gluten-Free",
    "dairy-free": "Dairy-Free",
    keto: "Ketogenic",
    paleo: "Paleo",
    "low-carb": "Low Carb",
    "low-fat": "Low Fat",
    "low-sodium": "Low Sodium",
    "diabetic-friendly": "Diabetic Friendly",
  }
  return labels[restriction]
}

// Get health goal display names
export const getHealthGoalLabel = (goal: HealthGoal): string => {
  const labels: Record<HealthGoal, string> = {
    "weight-loss": "Weight Loss",
    "muscle-gain": "Muscle Gain",
    "heart-health": "Heart Health",
    "diabetes-management": "Diabetes Management",
    "general-wellness": "General Wellness",
    "energy-boost": "Energy Boost",
    "digestive-health": "Digestive Health",
  }
  return labels[goal]
}
