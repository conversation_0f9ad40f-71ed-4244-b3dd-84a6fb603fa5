"use client"

import { useState, useEffect } from "react"
import { ArrowLeft, AlertCircle, CheckCircle, Loader2, User } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CameraScanner } from "@/components/camera-scanner"
import { HealthScoreBadge } from "@/components/health-score-badge"
import { ProductRecommendations } from "@/components/product-recommendations"
import { processImageWithOCR, type OCRResult } from "@/lib/ocr-processor"
import { calculatePersonalizedScore, type PersonalizedScoreResult } from "@/lib/personalized-scoring"
import { loadUserProfile, addScanToHistory } from "@/lib/user-profile"
import { searchProductByBarcode, type ProductData } from "@/lib/product-database"
import Link from "next/link"

interface ScanResult {
  productName: string
  ocrResult: OCRResult
  personalizedResult: PersonalizedScoreResult
  productData?: ProductData
}

export default function ScanPage() {
  const [capturedImage, setCapturedImage] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingStep, setProcessingStep] = useState<string>("")
  const [scanResult, setScanResult] = useState<ScanResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [userProfile, setUserProfile] = useState(loadUserProfile())

  useEffect(() => {
    setUserProfile(loadUserProfile())
  }, [])

  const handleImageCapture = async (imageData: string) => {
    setCapturedImage(imageData)
    setIsProcessing(true)
    setError(null)
    setScanResult(null)

    try {
      setProcessingStep("Scanning barcode...")

      // Process image with OCR
      const ocrResult = await processImageWithOCR(imageData)

      let productData: ProductData | null = null
      if (ocrResult.barcode) {
        setProcessingStep("Looking up product information...")
        try {
          productData = await searchProductByBarcode(ocrResult.barcode)
        } catch (err) {
          console.warn("Product lookup failed:", err)
          // Continue with OCR data only
        }
      }

      setProcessingStep("Extracting nutrition information...")

      const nutritionFacts = productData?.nutritionFacts
        ? {
            calories: productData.nutritionFacts.energy
              ? Math.round(productData.nutritionFacts.energy / 4.184)
              : ocrResult.nutritionFacts.calories,
            totalFat: productData.nutritionFacts.fat?.toString() || ocrResult.nutritionFacts.totalFat,
            saturatedFat: productData.nutritionFacts.saturatedFat?.toString() || ocrResult.nutritionFacts.saturatedFat,
            sodium: productData.nutritionFacts.sodium?.toString() || ocrResult.nutritionFacts.sodium,
            totalCarbs: productData.nutritionFacts.carbohydrates?.toString() || ocrResult.nutritionFacts.totalCarbs,
            sugars: productData.nutritionFacts.sugars?.toString() || ocrResult.nutritionFacts.sugars,
            protein: productData.nutritionFacts.proteins?.toString() || ocrResult.nutritionFacts.protein,
            dietaryFiber: productData.nutritionFacts.fiber?.toString() || ocrResult.nutritionFacts.dietaryFiber,
          }
        : ocrResult.nutritionFacts

      const ingredients = productData?.ingredients || ocrResult.ingredients

      // Calculate personalized score if user has profile
      const personalizedResult = userProfile
        ? calculatePersonalizedScore(nutritionFacts, ingredients, userProfile.preferences)
        : {
            baseScore: 75, // Default score
            personalizedScore: 75,
            adjustments: [],
            warnings: [],
            recommendations: [],
          }

      setProcessingStep("Analyzing health impact...")

      const result: ScanResult = {
        productName: productData?.productName || ocrResult.productName || "Unknown Product",
        ocrResult: {
          ...ocrResult,
          nutritionFacts,
          ingredients,
        },
        personalizedResult,
        productData: productData || undefined,
      }

      setScanResult(result)

      // Add to scan history if user has profile
      if (userProfile) {
        addScanToHistory({
          productName: result.productName,
          healthScore: personalizedResult.personalizedScore,
          nutritionFacts: nutritionFacts,
          ingredients: ingredients,
          imageData: imageData,
        })
      }
    } catch (err: any) {
      console.error("Processing error:", err)
      setError(err.message || "Failed to process the image. Please try again with a clearer image.")
    } finally {
      setIsProcessing(false)
      setProcessingStep("")
    }
  }

  const resetScan = () => {
    setCapturedImage(null)
    setScanResult(null)
    setError(null)
    setIsProcessing(false)
    setProcessingStep("")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/">
                <Button variant="ghost" size="sm" className="mr-4">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
              </Link>
              <h1 className="text-xl font-semibold">Scan Product Label</h1>
            </div>
            <Link href="/profile">
              <Button variant="outline" size="sm">
                <User className="h-4 w-4 mr-2" />
                Profile
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto mb-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Demo Mode:</strong> This app simulates OCR scanning with realistic sample data. Different images
              will show varied nutrition information to demonstrate the functionality.
            </AlertDescription>
          </Alert>
        </div>

        {/* Profile prompt for new users */}
        {!userProfile && !capturedImage && !scanResult && (
          <div className="max-w-md mx-auto mb-6">
            <Alert>
              <User className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <span>Set up your profile for personalized health scores!</span>
                  <Link href="/profile">
                    <Button size="sm" variant="outline">
                      Setup
                    </Button>
                  </Link>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        )}

        {!capturedImage && !scanResult && (
          <div className="max-w-md mx-auto">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Ready to Scan</h2>
              <p className="text-gray-600">Point your camera at a product label or upload an image to get started.</p>
            </div>
            <CameraScanner onImageCapture={handleImageCapture} />
          </div>
        )}

        {capturedImage && isProcessing && (
          <div className="max-w-md mx-auto text-center">
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <img src={capturedImage || "/placeholder.svg"} alt="Captured label" className="w-full rounded-lg mb-4" />
              <div className="flex items-center justify-center space-x-2 mb-4">
                <Loader2 className="h-6 w-6 animate-spin text-green-600" />
                <span className="text-gray-600">Processing image...</span>
              </div>
              {processingStep && <p className="text-sm text-gray-500 mb-2">{processingStep}</p>}
              <p className="text-xs text-gray-400">This may take 10-30 seconds</p>
            </div>
          </div>
        )}

        {error && (
          <div className="max-w-md mx-auto">
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button onClick={resetScan} className="w-full">
              Try Again
            </Button>
          </div>
        )}

        {scanResult && (
          <div className="max-w-2xl mx-auto space-y-6">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              {/* Result Header */}
              <div className="p-6 border-b">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-semibold">{scanResult.productName}</h3>
                    {scanResult.productData?.brand && (
                      <p className="text-sm text-gray-600">{scanResult.productData.brand}</p>
                    )}
                    {scanResult.productData?.nutritionGrade && (
                      <div className="mt-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Nutri-Score: {scanResult.productData.nutritionGrade.toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {userProfile &&
                      scanResult.personalizedResult.baseScore !== scanResult.personalizedResult.personalizedScore && (
                        <div className="text-sm text-gray-500">
                          Base: <HealthScoreBadge score={scanResult.personalizedResult.baseScore} size="sm" />
                        </div>
                      )}
                    <HealthScoreBadge score={scanResult.personalizedResult.personalizedScore} size="lg" />
                  </div>
                </div>
                {capturedImage && (
                  <img
                    src={capturedImage || "/placeholder.svg"}
                    alt="Scanned label"
                    className="w-full max-w-sm rounded-lg mx-auto"
                  />
                )}
                {scanResult.ocrResult.barcode && (
                  <p className="text-sm text-gray-500 mt-2 text-center">Barcode: {scanResult.ocrResult.barcode}</p>
                )}
              </div>

              {/* Personalized Warnings */}
              {scanResult.personalizedResult.warnings.length > 0 && (
                <div className="p-6 border-b bg-red-50">
                  <h4 className="font-semibold mb-3 text-red-800 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Important Warnings
                  </h4>
                  <ul className="space-y-2">
                    {scanResult.personalizedResult.warnings.map((warning, index) => (
                      <li key={index} className="text-sm text-red-700 flex items-start">
                        <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-2 mt-2 flex-shrink-0"></span>
                        {warning}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Score Adjustments */}
              {userProfile && scanResult.personalizedResult.adjustments.length > 0 && (
                <div className="p-6 border-b">
                  <h4 className="font-semibold mb-3">Personalized Adjustments</h4>
                  <div className="space-y-2">
                    {scanResult.personalizedResult.adjustments.map((adjustment, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">{adjustment.reason}</span>
                        <span
                          className={`font-medium ${adjustment.adjustment > 0 ? "text-green-600" : "text-red-600"}`}
                        >
                          {adjustment.adjustment > 0 ? "+" : ""}
                          {adjustment.adjustment}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Nutrition Facts */}
              {Object.keys(scanResult.ocrResult.nutritionFacts).length > 0 && (
                <div className="p-6 border-b">
                  <h4 className="font-semibold mb-3">Nutrition Facts</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    {scanResult.ocrResult.nutritionFacts.calories && (
                      <div>
                        <span className="text-gray-600">Calories:</span>
                        <span className="ml-2 font-medium">{scanResult.ocrResult.nutritionFacts.calories}</span>
                      </div>
                    )}
                    {scanResult.ocrResult.nutritionFacts.totalFat && (
                      <div>
                        <span className="text-gray-600">Total Fat:</span>
                        <span className="ml-2 font-medium">{scanResult.ocrResult.nutritionFacts.totalFat}g</span>
                      </div>
                    )}
                    {scanResult.ocrResult.nutritionFacts.sodium && (
                      <div>
                        <span className="text-gray-600">Sodium:</span>
                        <span className="ml-2 font-medium">{scanResult.ocrResult.nutritionFacts.sodium}mg</span>
                      </div>
                    )}
                    {scanResult.ocrResult.nutritionFacts.totalCarbs && (
                      <div>
                        <span className="text-gray-600">Total Carbs:</span>
                        <span className="ml-2 font-medium">{scanResult.ocrResult.nutritionFacts.totalCarbs}g</span>
                      </div>
                    )}
                    {scanResult.ocrResult.nutritionFacts.sugars && (
                      <div>
                        <span className="text-gray-600">Sugars:</span>
                        <span className="ml-2 font-medium">{scanResult.ocrResult.nutritionFacts.sugars}g</span>
                      </div>
                    )}
                    {scanResult.ocrResult.nutritionFacts.protein && (
                      <div>
                        <span className="text-gray-600">Protein:</span>
                        <span className="ml-2 font-medium">{scanResult.ocrResult.nutritionFacts.protein}g</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Ingredients */}
              {scanResult.ocrResult.ingredients.length > 0 && (
                <div className="p-6">
                  <h4 className="font-semibold mb-3">Ingredients</h4>
                  <div className="flex flex-wrap gap-2">
                    {scanResult.ocrResult.ingredients.map((ingredient: string, index: number) => (
                      <span key={index} className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm border">
                        {ingredient}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendations */}
              {scanResult.personalizedResult.recommendations.length > 0 && (
                <div className="p-6 border-b bg-blue-50">
                  <h4 className="font-semibold mb-3 text-blue-800">Recommendations</h4>
                  <ul className="space-y-2">
                    {scanResult.personalizedResult.recommendations.map((recommendation, index) => (
                      <li key={index} className="text-sm text-blue-700 flex items-start">
                        <CheckCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                        {recommendation}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {scanResult.productData && (
              <ProductRecommendations
                currentProduct={scanResult.productData}
                userPreferences={userProfile?.preferences}
              />
            )}

            {/* Action Buttons */}
            <div className="mt-6 flex space-x-4">
              <Button onClick={resetScan} variant="outline" className="flex-1 bg-transparent">
                Scan Another
              </Button>
              {!userProfile ? (
                <Link href="/profile" className="flex-1">
                  <Button className="w-full bg-green-600 hover:bg-green-700">Setup Profile</Button>
                </Link>
              ) : (
                <Link href="/profile" className="flex-1">
                  <Button className="w-full bg-green-600 hover:bg-green-700">View History</Button>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
