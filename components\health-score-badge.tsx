import { cn } from "@/lib/utils"

interface HealthScoreBadgeProps {
  score: number
  size?: "sm" | "md" | "lg"
  className?: string
}

export function HealthScoreBadge({ score, size = "md", className }: HealthScoreBadgeProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500 text-white"
    if (score >= 60) return "bg-yellow-500 text-white"
    if (score >= 40) return "bg-orange-500 text-white"
    return "bg-red-500 text-white"
  }

  const getScoreLabel = (score: number) => {
    if (score >= 80) return "Excellent"
    if (score >= 60) return "Good"
    if (score >= 40) return "Fair"
    return "Poor"
  }

  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    md: "text-sm px-3 py-1.5",
    lg: "text-base px-4 py-2",
  }

  return (
    <div
      className={cn(
        "inline-flex items-center rounded-full font-semibold",
        getScoreColor(score),
        sizeClasses[size],
        className,
      )}
    >
      <span className="mr-1">{score}</span>
      <span className="text-xs opacity-90">/ 100</span>
      <span className="ml-2 text-xs opacity-90">{getScoreLabel(score)}</span>
    </div>
  )
}
