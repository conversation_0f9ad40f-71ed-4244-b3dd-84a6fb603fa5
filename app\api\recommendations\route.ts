import { type NextRequest, NextResponse } from "next/server"
import { getProductRecommendations } from "@/lib/product-database"

export async function POST(request: NextRequest) {
  try {
    const { product, userPreferences } = await request.json()

    if (!product) {
      return NextResponse.json({ error: "Product data is required" }, { status: 400 })
    }

    const recommendations = await getProductRecommendations(product, userPreferences)

    return NextResponse.json({ recommendations })
  } catch (error) {
    console.error("Error in recommendations API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
